package com.heal.controlcenter.businesslogic;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.ServiceKpiThresholds;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.ServiceDao;
import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.OperationTypeEnum;
import com.heal.controlcenter.pojo.StaticThresholdRules;
import com.heal.controlcenter.service.InstallationAttributeService;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PostServiceStaticThresholdBL implements BusinessLogic<List<StaticThresholdRules>, UtilityBean<List<StaticThresholdRules>>, List<StaticThresholdRules>>{

    @Autowired
    private CommonUtils commonUtils;

    @Autowired
    private AccountsDao accountsDao;

    @Autowired
    private ControllerDao controllerDao;

    @Autowired
    private UserValidationUtil userValidationUtil;

    @Autowired
    private MasterDataDao masterDataDao;

    @Autowired
    private GetServiceStaticThresholdBL getServiceStaticThresholdBL;

    @Autowired
    private ServiceDao serviceDao;

    @Autowired
    private InstallationAttributeService installationAttributeService;

    @Autowired
    private DateTimeUtil dateTimeUtil;

    @Autowired
    private ServiceRepo serviceRepo;

    @Autowired
    private ServiceOpensearchRepo serviceOpensearchRepoOpenSearch;

    @Autowired
    private CacheWrapper cacheWrapper;

    @Autowired
    private ClientValidationUtils clientValidationUtils;

    @Autowired
    private ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<List<StaticThresholdRules>> clientValidation(List<StaticThresholdRules> sorRuleList, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.nullOrEmptyCheck(authKey, UIMessages.AUTH_KEY_INVALID);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.nullOrEmptyCheck(accountIdentifier, UIMessages.ACCOUNT_IDENTIFIER_INVALID);

        String serviceIdentifier = requestParams[2];
        clientValidationUtils.nullOrEmptyCheck(serviceIdentifier, UIMessages.SERVICE_IDENTIFIER_INVALID);

        String kpiType = requestParams[3].trim();
        clientValidationUtils.nullOrEmptyCheck(kpiType, UIMessages.KPI_TYPE_INVALID);

        String thresholdType = requestParams[4].trim();
        if (thresholdType.trim().isEmpty() || !thresholdType.equalsIgnoreCase("static")) {
            log.error("Invalid thresholdType. Reason: thresholdType is undefined in the request.");
            throw new ClientException(UIMessages.THRESHOLD_TYPE_INVALID);
        }

        if (sorRuleList == null || sorRuleList.isEmpty()){
            log.error("Received empty list of availability Rule objects");
            throw new ClientException("Static thresholds are unavailable");
        }
        Set<StaticThresholdRules> validSet = new HashSet<>(sorRuleList);
        if (sorRuleList.size() != validSet.size()) {
            log.error("duplicate static thresholds are defined for the same KPI");
            throw new ClientException("duplicate static thresholds are defined for the same KPI");
        }

        HashMap<String,String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, requestParams[1]);
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, requestParams[2]);
        requestParamsMap.put(Constants.KPI_TYPE, kpiType);
        requestParamsMap.put(Constants.THRESHOLD_TYPE, thresholdType);
        requestParamsMap.put(Constants.AUTH_KEY, authKey);
        return UtilityBean.<List<StaticThresholdRules>>builder().pojoObject(sorRuleList).requestParams(requestParamsMap).build();
    }

    @Override
    public UtilityBean<List<StaticThresholdRules>> serverValidation(UtilityBean<List<StaticThresholdRules>> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);
        String kpiType = utilityBean.getRequestParams().get(Constants.KPI_TYPE).trim();
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);

        Map<String, Object> metadata = new HashMap<>();

        String userId = serverValidationUtils.authKeyValidation(authKey);

        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        metadata.put(Constants.ACCOUNT, account);

        UserAccessDetails userAccessDetails = serverValidationUtils.userAccessDetailsValidation(userId, accountIdentifier);

        Service service = serverValidationUtils.serviceValidation(userId, accountIdentifier,serviceIdentifier,userAccessDetails);
        metadata.put(Constants.SERVICE, service);

        Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
        ViewTypes kpi = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.KPI_TYPE, kpiType);
        if (kpi == null || (!kpi.getSubTypeName().equalsIgnoreCase("Availability") || !kpi.getSubTypeName().equalsIgnoreCase("Core"))) {
            log.error("Invalid KPI type [{}]. Reason: KPI type should be one of Availability or Core.", kpiType);
            throw new ServerException(UIMessages.INVALID_KPI_TYPE);
        }
        metadata.put(kpiType, kpi);

        String invalidViewTypeMessage = "Invalid thresholds view types. Subtype: {} unavailable";
        ViewTypes lowThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        if (lowThresholdSeverityType == null) {
            log.error(invalidViewTypeMessage, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, lowThresholdSeverityType);

        ViewTypes mediumThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        if (mediumThresholdSeverityType == null) {
            log.error(invalidViewTypeMessage, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, mediumThresholdSeverityType);

        ViewTypes highThresholdSeverityType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        if (highThresholdSeverityType == null) {
            log.error(invalidViewTypeMessage, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, highThresholdSeverityType);

        ViewTypes lessThanOperationType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_LESSER_THAN);
        String invalidOperationViewTypeError = "Invalid operation view types. Subtype: {} unavailable";
        if (lessThanOperationType == null) {
            log.error(invalidOperationViewTypeError, Constants.OPERATIONS_TYPE_LESSER_THAN);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.OPERATIONS_TYPE_LESSER_THAN, lessThanOperationType);

        ViewTypes greaterThanOperationType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_GREATER_THAN);
        if (greaterThanOperationType == null) {
            log.error(invalidOperationViewTypeError, Constants.OPERATIONS_TYPE_GREATER_THAN);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.OPERATIONS_TYPE_GREATER_THAN, greaterThanOperationType);

        ViewTypes notBetweenOperationType = commonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_NOT_BETWEEN);
        if (notBetweenOperationType == null) {
            log.error(invalidOperationViewTypeError, Constants.OPERATIONS_TYPE_NOT_BETWEEN);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.OPERATIONS_TYPE_NOT_BETWEEN, notBetweenOperationType);

        UtilityBean bean = UtilityBean.<List<StaticThresholdRules>>builder()
                .metadata(Map.of(Constants.USER_ID_KEY, userId))
                .requestParams(utilityBean.getRequestParams())
                .account(AccountBean.builder().id(account.getId())
                        .identifier(account.getIdentifier())
                        .name(account.getName())
                        .lastModifiedBy(account.getLastModifiedBy())
                        .build())
                .pojoObject(utilityBean.getPojoObject())
                .metadata(metadata).build();

        List<StaticThresholdRules> validRulePojo = validateInputThreshold(bean, kpi, account, service);

        utilityBean.setPojoObject(validRulePojo);
        return utilityBean;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<StaticThresholdRules> process(UtilityBean<List<StaticThresholdRules>> bean) throws DataProcessingException {
        String accountIdString = bean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdentifier = bean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);
        String kpiType = bean.getRequestParams().get(Constants.KPI_TYPE);
        String userId = (String) bean.getMetadata().get(Constants.USER_ID_KEY);

        Map<String,Object> metaData = bean.getMetadata();
        ViewTypes lowThresholdSeverityType = (ViewTypes) metaData.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThresholdSeverityType = (ViewTypes) metaData.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThresholdSeverityType = (ViewTypes) metaData.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        ViewTypes kpi = (ViewTypes) metaData.get(kpiType);
        Account account = (Account) metaData.get(Constants.ACCOUNT);
        Service service = (Service) metaData.get(Constants.SERVICE);

        int lowThresholdSeverityTypeId = lowThresholdSeverityType.getSubTypeId();
        int mediumThresholdSeverityTypeId = mediumThresholdSeverityType.getSubTypeId();
        int highThresholdSeverityTypeId = highThresholdSeverityType.getSubTypeId();
        int accountId = account.getId();
        int serviceId = service.getId();

        List<StaticThresholdRules> validRulePojo = bean.getPojoObject();
        try {
            Timestamp echoMilli = dateTimeUtil.getCurrentTimestampInGMT();
            Timestamp echoMilliPlusOneMin = new Timestamp(dateTimeUtil.getCurrentTimestampInGMT().getTime() + 60000);

            HashMap<StaticThresholdRules,List<ServiceKpiThreshold>> addRulesList = new HashMap<>();
            HashMap<StaticThresholdRules,List<ServiceKpiThreshold>> updateRulesList = new HashMap<>();
            Map<String, List<ViewTypes>> allViewTypesIdMap = cacheWrapper.getAllViewTypesIdMap();

            validRulePojo.forEach(rulePojo -> {
                ArrayList<ServiceKpiThreshold> addServiceKpiThresholds = new ArrayList<>();
                ArrayList<ServiceKpiThreshold> updatedServiceKpiThresholds = new ArrayList<>();
                if(rulePojo.getLowThreshold() != null){
                    ServiceKpiThreshold lowThresholdBean = populateServiceKpiThreshold(rulePojo, accountId, serviceId, echoMilli, userId, kpi, rulePojo.getLowThreshold(), lowThresholdSeverityTypeId, allViewTypesIdMap);
                    if(rulePojo.getLowThreshold().getDataId() == 0){
                        addServiceKpiThresholds.add(lowThresholdBean);
                    }
                    else{
                        updatedServiceKpiThresholds.add(lowThresholdBean);
                    }
                }

                if(rulePojo.getWarningThreshold() != null){
                    ServiceKpiThreshold mediumThresholdBean = populateServiceKpiThreshold(rulePojo, accountId, serviceId, echoMilli, userId, kpi, rulePojo.getWarningThreshold(), mediumThresholdSeverityTypeId, allViewTypesIdMap);
                    if(rulePojo.getWarningThreshold().getDataId() == 0){
                        addServiceKpiThresholds.add(mediumThresholdBean);
                    }
                    else{
                        updatedServiceKpiThresholds.add(mediumThresholdBean);
                    }
                }

                if(rulePojo.getErrorThreshold() != null){
                    ServiceKpiThreshold highThresholdBean = populateServiceKpiThreshold(rulePojo, accountId, serviceId, echoMilli, userId, kpi, rulePojo.getErrorThreshold(), highThresholdSeverityTypeId, allViewTypesIdMap);
                    if(rulePojo.getErrorThreshold().getDataId() == 0){
                        addServiceKpiThresholds.add(highThresholdBean);
                    }
                    else{
                        updatedServiceKpiThresholds.add(highThresholdBean);
                    }
                }

                addRulesList.put(rulePojo, addServiceKpiThresholds);
                updateRulesList.put(rulePojo, updatedServiceKpiThresholds);
            });

            addRulesList.forEach((sorRule,serviceKpiThresholds) -> {
                if(serviceKpiThresholds != null && !serviceKpiThresholds.isEmpty()){
                    int[] thresholdIds = serviceDao.addThreshold(serviceKpiThresholds);
                    log.info("Threshold value is successfully inserted into 'service_kpi_thresholds' table - : {}", thresholdIds);
                }
            });

            updateRulesList.forEach((sorRule,updateSorRules) -> {
                if(updateSorRules != null && !updateSorRules.isEmpty()){
                    int[] thresholdIds = serviceDao.updateThreshold(updateSorRules);
                    log.info("Threshold value is successfully updated into 'service_kpi_thresholds' table - : {} ", thresholdIds);
                    List<ServiceKpiThresholds> configureKpiList;
                    try {
                        configureKpiList = serviceOpensearchRepoOpenSearch.getServiceLevelThresholdDetails(accountIdString, serviceIdentifier, sorRule.getKpiId(), sorRule.getKpiLevel());
                    } catch (ControlCenterException e) {
                        log.error("Error while fetching service level threshold details from opensearch. Details: {}", e.getMessage());
                        throw new RuntimeException(e);
                    }
                    if (!configureKpiList.isEmpty()) {
                        configureKpiList.sort(Comparator.comparing(ServiceKpiThresholds::getStartTime).reversed());
                        ServiceKpiThresholds configKpiDetails = configureKpiList.get(0);
                        serviceOpensearchRepoOpenSearch.updateThreshold(accountIdString, serviceIdentifier, sorRule, configKpiDetails, echoMilli);
                    }
                }
            });

            //bulk insert into opensearch
            List<StaticThresholdRules> generateAnomalyRules = validRulePojo.stream()
                    .filter(StaticThresholdRules::isGenerateAnomaly)
                    .collect(Collectors.toList());
            serviceOpensearchRepoOpenSearch.insertBulkServiceKpiThresholdsIntoOS(accountIdString, serviceIdentifier, generateAnomalyRules, echoMilliPlusOneMin, "");

            //update in redis
            updateServiceKpiThresholdsInRedis(accountIdString,serviceIdentifier, validRulePojo, echoMilli, lowThresholdSeverityTypeId, mediumThresholdSeverityTypeId, highThresholdSeverityTypeId);
        } catch (Exception e) {
            log.error("Exception encountered while processing thresholds. Details ", e);
            e.printStackTrace();
            throw new DataProcessingException(e.getMessage());
        }
        return validRulePojo;
    }

    private List<StaticThresholdRules> getStaticThresholdRules(UtilityBean<List<StaticThresholdRules>> bean) throws ServerException {
        String userId = (String) bean.getMetadata().get("UserId");
        Map<String,Object> metadata = bean.getMetadata();
        UtilityBean<Map<String, Object>> utilityBean = UtilityBean.<Map<String, Object>>builder()
                .pojoObject(metadata)
                .requestParams(bean.getRequestParams())
                .metadata(Map.of("UserId",userId))
                .build();

        try{
            return getServiceStaticThresholdBL.process(utilityBean);
        }
        catch(DataProcessingException e){
            log.error("Error while fetching static threshold rules. Details", e);
            throw new ServerException("Error while fetching static threshold rules");
        }
    }

    private void validKpiCheck(StaticThresholdRules staticThresholdRules, int accountId, int serviceId, List<StaticThresholdRules> validKpis, int lowThresholdSeverityTypeId, int mediumThresholdSeverityTypeId, int highThresholdSeverityTypeId) throws ControlCenterException, ServerException {
        List<ServiceKpiThreshold> temp = serviceDao.getKpiThresholdsByApplicableToAndAttribute(accountId, serviceId, staticThresholdRules.getKpiLevel(), staticThresholdRules.getKpiAttribute());
        if (temp != null && !temp.isEmpty()) {
            for (ServiceKpiThreshold kpi : temp) {
                if(String.valueOf(kpi.getKpiId()).equals(staticThresholdRules.getKpiId()) && kpi.getApplicableTo().equals(staticThresholdRules.getKpiLevel())) {
                    if(kpi.getThresholdSeverityId() == lowThresholdSeverityTypeId && staticThresholdRules.getLowThreshold() != null){
                        staticThresholdRules.getLowThreshold().setDataId(kpi.getId());
                    }
                    else if(kpi.getThresholdSeverityId() == mediumThresholdSeverityTypeId && staticThresholdRules.getWarningThreshold() != null){
                        staticThresholdRules.getWarningThreshold().setDataId(kpi.getId());
                    }
                    else if(kpi.getThresholdSeverityId() == highThresholdSeverityTypeId && staticThresholdRules.getErrorThreshold() != null) {
                        staticThresholdRules.getErrorThreshold().setDataId(kpi.getId());
                    }
                }
            }
        } else {
            long count = validKpis.stream().filter(kpis -> kpis.getKpiId().equals(staticThresholdRules.getKpiId()) && kpis.getKpiLevel().equals(staticThresholdRules.getKpiLevel())).count();
            if(count == 0){
                String message = String.format("No existing thresholds found for the given KPI ID and level. KPI ID: %s, Level: %s", staticThresholdRules.getKpiId(), staticThresholdRules.getKpiLevel());
                log.error(message);
                throw new ServerException(message);
            }
        }
    }

    private ServiceKpiThreshold populateServiceKpiThreshold(StaticThresholdRules updateSorRule, int accountId, int serviceId, Timestamp echoMilli, String userId, ViewTypes kpiType, ThresholdConfig thresholds, int thresholdSeverityTypeId, Map<String, List<ViewTypes>> allViewTypesIdMap) {
        ServiceKpiThreshold updateThresholdBean = new ServiceKpiThreshold();
        updateThresholdBean.setThresholdSeverityId(thresholdSeverityTypeId);
        updateThresholdBean.setId(updateSorRule.getDataId());
        updateThresholdBean.setKpiId(Integer.parseInt(updateSorRule.getKpiId()));
        updateThresholdBean.setAccountId(accountId);
        updateThresholdBean.setServiceId(serviceId);
        updateThresholdBean.setUpdatedTime(echoMilli);
        updateThresholdBean.setKpiAttribute(updateSorRule.getKpiAttribute());
        updateThresholdBean.setUserDetailsId(userId);
        updateThresholdBean.setApplicableTo(updateSorRule.getKpiLevel());
        updateThresholdBean.setCreatedTime(echoMilli);
        updateThresholdBean.setStartTime(echoMilli);
        updateThresholdBean.setId(thresholds.getDataId());
        updateThresholdBean.setStatus(thresholds.getStatus());
        updateThresholdBean.setCoverageWindow(updateSorRule.getCoverageWindow());

        updateThresholdBean.setDefinedBy(Constants.THRESHOLD_DEFINED_BY_USER);
        if (kpiType.getSubTypeName().equals(Constants.CORE_KPI_TYPE) && thresholds.getOperationType() != null) {
            log.debug("CORE kpi type provided");
            Double max = thresholds.getMax();
            Double min = thresholds.getMin();
            ViewTypes operationType = commonUtils.getViewTypeByNameAndSubType(allViewTypesIdMap, Constants.OPERATIONS_TYPE, thresholds.getOperationType());
            updateThresholdBean.setOperationTypeId(operationType.getSubTypeId());
            if (max != null && min != null && max > min && operationType.getSubTypeName().equals(OperationTypeEnum.NOT_BETWEEN.getType())) {
                updateThresholdBean.setMaxThreshold(max);
                updateThresholdBean.setMinThreshold(min);
            } else if (min != null) {
                updateThresholdBean.setMinThreshold(min);
            }
        } else if (kpiType.getSubTypeName().equals(Constants.AVAIL_KPI_TYPE)) {
            ViewTypes operationType = commonUtils.getViewTypeByNameAndSubType(allViewTypesIdMap, Constants.AVAILABILITY_OPERATIONS_TYPE, thresholds.getOperationType());
            updateThresholdBean.setOperationTypeId(operationType.getSubTypeId());
        }
        return updateThresholdBean;
    }

    private void updateServiceKpiThresholdsInRedis(String accountIdString, String serviceIdentifier, List<StaticThresholdRules> sorRuleList, Timestamp echoMilli, int lowThresholdSeverityTypeId, int mediumThresholdSeverityTypeId, int highThresholdSeverityTypeId) {
        log.info("Updating service KPI thresholds in Redis for accountIdentifier: {}, serviceIdentifier: {}", accountIdString, serviceIdentifier);
        for (StaticThresholdRules staticThresholdRule : sorRuleList) {
            KpiDetails serviceKPI = serviceRepo.getServiceKPI(accountIdString, serviceIdentifier, Integer.parseInt(staticThresholdRule.getKpiId()));
            if (serviceKPI == null) {
                log.error("Service KPI Details for accountIdentifier {} - serviceIdentifier {} - KPI ID {} does not exist in redis", accountIdString, serviceIdentifier, staticThresholdRule.getKpiId());
                continue;
            }

            List<KpiViolationConfig> newKPIViolationConfigToBeAdded = buildKPIViolationConfigObject(echoMilli, staticThresholdRule, serviceKPI.getKpiViolationConfig(), lowThresholdSeverityTypeId, mediumThresholdSeverityTypeId, highThresholdSeverityTypeId);
            Map<String, List<KpiViolationConfig>> kpiViolationMapByAttr = newKPIViolationConfigToBeAdded.stream()
                    .collect(Collectors.groupingBy(KpiViolationConfig::getAttributeValue));
            serviceKPI.setKpiViolationConfig(kpiViolationMapByAttr);
            serviceRepo.updateServiceKpiById(accountIdString, serviceIdentifier, serviceKPI.getId(), serviceKPI);
            serviceRepo.updateServiceKpiByIdentifier(accountIdString, serviceIdentifier, serviceKPI.getIdentifier(), serviceKPI);
        }
    }

    private List<KpiViolationConfig> buildKPIViolationConfigObject(Timestamp echoMilli, StaticThresholdRules staticThresholdRule, Map<String, List<KpiViolationConfig>> existingKpiViolationConfigMap, int lowThresholdSeverityTypeId, int mediumThresholdSeverityTypeId, int highThresholdSeverityTypeId) {
        SimpleDateFormat formatter = new SimpleDateFormat(Constants.DATE_TIME);
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        List<KpiViolationConfig> kpiViolationConfigs = new ArrayList<>();
        if(staticThresholdRule.getLowThreshold() != null){
            KpiViolationConfig lowThresholdConfig = getKpiViolationConfig(echoMilli, staticThresholdRule, formatter, staticThresholdRule.getLowThreshold());
            lowThresholdConfig.setThresholdSeverityId(lowThresholdSeverityTypeId);
            lowThresholdConfig.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            kpiViolationConfigs.add(lowThresholdConfig);
        }

        if(staticThresholdRule.getWarningThreshold() != null){
            KpiViolationConfig warningThresholdConfig = getKpiViolationConfig(echoMilli, staticThresholdRule, formatter, staticThresholdRule.getWarningThreshold());
            warningThresholdConfig.setThresholdSeverityId(mediumThresholdSeverityTypeId);
            warningThresholdConfig.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            kpiViolationConfigs.add(warningThresholdConfig);
        }

        if(staticThresholdRule.getErrorThreshold() != null){
            KpiViolationConfig errorThresholdConfig = getKpiViolationConfig(echoMilli, staticThresholdRule, formatter, staticThresholdRule.getErrorThreshold());
            errorThresholdConfig.setThresholdSeverityId(highThresholdSeverityTypeId);
            errorThresholdConfig.setThresholdSeverity(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            kpiViolationConfigs.add(errorThresholdConfig);
        }

        List<KpiViolationConfig> existingKpiViolationConfig = new ArrayList<>();
        for (Map.Entry<String, List<KpiViolationConfig>> entry : existingKpiViolationConfigMap.entrySet()) {
            existingKpiViolationConfig.addAll(entry.getValue());
        }

        return extractAndUpdateUpdatedKpiViolationConfigs(kpiViolationConfigs, existingKpiViolationConfig);
    }

    private List<KpiViolationConfig> extractAndUpdateUpdatedKpiViolationConfigs(List<KpiViolationConfig> kpiViolationConfigs, List<KpiViolationConfig> existingKpiViolationConfig) {
        List<KpiViolationConfig> finalKpiViolationConfigs = new ArrayList<>();
        if (existingKpiViolationConfig.isEmpty()) {
            return kpiViolationConfigs;
        } else {
            for (KpiViolationConfig existingConfig : existingKpiViolationConfig) {
                boolean isReplaced = false;
                for (KpiViolationConfig newConfig : kpiViolationConfigs) {
                    if (newConfig.getKpiId() == existingConfig.getKpiId() &&
                            newConfig.getAttributeValue().equals(existingConfig.getAttributeValue()) &&
                            newConfig.getThresholdSeverityId() == existingConfig.getThresholdSeverityId() &&
                            newConfig.getApplicableTo().equals(existingConfig.getApplicableTo())) {
                        finalKpiViolationConfigs.add(newConfig);
                        isReplaced = true;
                        break;
                    }
                }
                if (!isReplaced) {
                    finalKpiViolationConfigs.add(existingConfig);
                }
            }

            for (KpiViolationConfig newConfig : kpiViolationConfigs) {
                boolean isPresent = finalKpiViolationConfigs.stream().anyMatch(config ->
                        config.getKpiId() == newConfig.getKpiId() &&
                                config.getAttributeValue().equals(newConfig.getAttributeValue()) &&
                                config.getThresholdSeverityId() == newConfig.getThresholdSeverityId() &&
                                config.getApplicableTo().equals(newConfig.getApplicableTo())
                );
                if (!isPresent) {
                    finalKpiViolationConfigs.add(newConfig);
                }
            }
        }

        return finalKpiViolationConfigs;
    }

    private KpiViolationConfig getKpiViolationConfig(Timestamp echoMilli, StaticThresholdRules staticThresholdRule, SimpleDateFormat formatter, ThresholdConfig thresholdConfigType) {
        return KpiViolationConfig.builder()
                .startTime(formatter.format(echoMilli.getTime()))
                .attributeValue(staticThresholdRule.getKpiAttribute())
                .kpiId(Integer.parseInt(staticThresholdRule.getKpiId()))
                .applicableTo(staticThresholdRule.getKpiLevel())
                .definedBy(staticThresholdRule.isUserDefinedSOR() ? Constants.THRESHOLD_DEFINED_BY_USER : Constants.THRESHOLD_DEFINED_BY_SYSTEM)
                .coverageWindow(staticThresholdRule.getCoverageWindow())
                .minThreshold(thresholdConfigType.getMin())
                .maxThreshold(thresholdConfigType.getMax())
                .operation(thresholdConfigType.getOperationType())
                .status(thresholdConfigType.getStatus())
                .build();
    }

    @NotNull
    private List<StaticThresholdRules> validateInputThreshold(UtilityBean<List<StaticThresholdRules>> bean, ViewTypes kpi, Account account, Service service) throws ServerException {
        List<StaticThresholdRules> allKpiLists = getStaticThresholdRules(bean);

        List<StaticThresholdRules> validRulePojo = new ArrayList<>();
        List<StaticThresholdRules> invalidJsonObjects = new ArrayList<>();
        ViewTypes lowThresholdSeverityType = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThresholdSeverityType = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThresholdSeverityType = (ViewTypes) bean.getMetadata().get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);

        List<StaticThresholdRules> sorRuleList = bean.getPojoObject();
        sorRuleList.forEach(rulesData -> {
            rulesData.validate(kpi.getSubTypeName());
            try {
                validKpiCheck(rulesData, account.getId(), service.getId(),allKpiLists, lowThresholdSeverityType.getSubTypeId(), mediumThresholdSeverityType.getSubTypeId(), highThresholdSeverityType.getSubTypeId());
            } catch (ControlCenterException | ServerException e){
                log.error("Error while validating KPI for service [{}]. Details: ", service.getIdentifier(), e);
                rulesData.getErrorMessage().put("Error while validating KPI",e.getMessage());
            }

            if (!rulesData.getErrorMessage().isEmpty()) {
                invalidJsonObjects.add(rulesData);
                log.error("Invalid Static Threshold Rule: {}",rulesData.getErrorMessage());
            } else {
                validRulePojo.add(rulesData);
            }
        });

        if(!invalidJsonObjects.isEmpty()) {
            log.error("Some static threshold rules are invalid. Please check the input JSON : {}", invalidJsonObjects);
            throw new ServerException("Some static threshold rules are invalid. Please check the input JSON.");
        }
        return validRulePojo;
    }
}
