package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.AddAccountBL;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.businesslogic.GetAccountServicesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.ErrorResponsePojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AccountController {

    @Autowired
    GetAccountsBL getAccountsBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    AddAccountBL addAccountBL;
    @Autowired
    GetAccountServicesBL getAccountServicesBL;

    @Operation(summary = "Retrieves all active accounts for the authenticated user.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Accounts fetched successfully.",
                            content = @Content(schema = @Schema(implementation = Account.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Exception encountered while fetching accounts.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Error occurred while fetching accounts.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    /**
     * Retrieves all active accounts for the authenticated user.
     *
     * <p>This endpoint performs the following actions:
     * <ul>
     *     <li>Validates the client request using the provided Bearer token.</li>
     *     <li>Performs server-side validation to extract user access details.</li>
     *     <li>Fetches the list of active accounts associated with the user.</li>
     * </ul>
     *
     * @param authorization the Bearer token for authenticating the user.
     * @return {@link ResponseEntity} containing the list of {@link Account} objects and a success message.
     * @throws ClientException if client input or token is invalid.
     * @throws ServerException if server-side validation fails.
     * @throws DataProcessingException if any error occurs during account data processing.
     */
    @GetMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<List<Account>>> getAllAccounts(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader("Authorization") String authorization)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<Account> utilityBean = getAccountsBL.clientValidation(null, authorization);
        UtilityBean<AccessDetailsBean> accessDetailsBean = getAccountsBL.serverValidation(utilityBean);
        List<Account> data = getAccountsBL.process(accessDetailsBean);

        ResponsePojo<List<Account>> responseBean = new ResponsePojo<>("Accounts fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Creates a new account",
            description = "Validates and creates a new account in the system. Performs DB insertions, tag mappings, and Redis updates.",
            security = @SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Account details for creation",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = Account.class)
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Account created successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = ResponsePojo.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))),
                    @ApiResponse(responseCode = "401", description = "Unauthorized",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))),
                    @ApiResponse(responseCode = "500", description = "Server error",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class)))
            }
    )
    /**
     * API endpoint to create a new account.
     *
     * <p>This method orchestrates the full account creation process by performing:
     * <ul>
     *     <li><b>Client-side validation</b> of the request payload and the authorization token.</li>
     *     <li><b>Server-side validation</b> to extract user context and enforce access control.</li>
     *     <li><b>Business logic processing</b> including database persistence, Redis caching,
     *         and any additional configuration (e.g., tags, anomalies).</li>
     * </ul>
     *
     * @param authorization The Bearer token used for user authentication, passed in the `Authorization` header.
     * @param body The {@link Account} object representing the incoming account creation payload.
     * @return A {@link ResponseEntity} containing a success message and HTTP headers.
     *
     * @throws ClientException If the input request is invalid (e.g., missing fields or format errors).
     * @throws ServerException If there are issues validating the user context or authorization token.
     * @throws DataProcessingException If an internal system error occurs during processing (e.g., DB or Redis failure).
     */
    @PostMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<Account>> createAccount(
            @RequestHeader("Authorization") String authorization,
            @Validated @RequestBody Account body) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: createAccount");

        UtilityBean<Account> utilityBean = addAccountBL.clientValidation(body, authorization, body.getIdentifier());
        UtilityBean<Account> updatedUtilityBean = addAccountBL.serverValidation(utilityBean);
        addAccountBL.process(updatedUtilityBean);

        ResponsePojo<Account> responseBean = new ResponsePojo<>("Account created successfully .", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Retrieve Services for Account",
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Services retrieved successfully",
                            content = @Content(
                                    schema = @Schema(
                                            implementation = List.class
                                    )
                            )
                    ),
                    @ApiResponse(
                            responseCode = "400", description = "Error in retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500", description = "Internal Server Exception encountered while retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @GetMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<List<Object>>> getAccountServices(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier)
            throws ClientException, DataProcessingException, ServerException {

        UtilityBean<Object> utilityBean = getAccountServicesBL.clientValidation(null, authorization, accountIdentifier);
        UtilityBean<Object> validatedBean = getAccountServicesBL.serverValidation(utilityBean);
        List<Object> services = getAccountServicesBL.process(validatedBean);

        ResponsePojo<List<Object>> responsePojo = new ResponsePojo<>("Services retrieved successfully",
                services, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}