package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetServiceAnomalyConfigBL;
import com.heal.controlcenter.businesslogic.UpdateServiceAnomalyConfigBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ErrorResponsePojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@Configuration
@RestController
public class ServiceConfigurationController {

    @Autowired
    UpdateServiceAnomalyConfigBL updateServiceAnomalyConfigBL;

    @Autowired
    GetServiceAnomalyConfigBL getServiceAnomalyConfigBL;

    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Update Service anomaly configuration",
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Service anomaly configuration updated successfully.",
                            content = @Content(schema = @Schema(implementation = Object.class))
                    ),
                    @ApiResponse(
                            responseCode = "400", description = "Invalid request body or parameters.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401", description = "Unauthorized access.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500", description = "Internal server error.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @PutMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/anomaly/configurations")
    public ResponseEntity<ResponsePojo<Object>> updateServiceAnomalyConfig(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    name = "serviceIdentifier",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceIdentifier") String serviceIdentifier,
            @Parameter(
                    description = "Service configuration details for anomaly suppression and persistence",
                    required = true
            )
            @RequestBody Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails)
            throws ClientException, DataProcessingException, ServerException {

        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> utilityBean =  updateServiceAnomalyConfigBL.clientValidation(serviceConfigDetails, authorization, accountIdentifier, serviceIdentifier);
        UtilityBean<Map<String, ServiceSuppPersistenceConfigPojo>> accountServiceKey = updateServiceAnomalyConfigBL.serverValidation(utilityBean);
        updateServiceAnomalyConfigBL.process(accountServiceKey);
        ResponsePojo<Object> responseBean = new ResponsePojo<>("Service Configuration updated successfully.", null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Get Service anomaly configuration",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Service anomaly configuration retrieved successfully.",
                            content = @Content(schema = @Schema(implementation = Map.class))
                    ),
                    @ApiResponse(responseCode = "400", description = "Invalid request parameters.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(responseCode = "401", description = "Unauthorized access.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(responseCode = "500", description = "Internal server error.",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    @GetMapping(value = "/accounts/{accountIdentifier}/services/{serviceIdentifier}/anomaly/configurations")
    public ResponseEntity<ResponsePojo<Map<String, ServiceSuppPersistenceConfigPojo>>> getServiceAnomalyConfig(
            @Parameter(
                    name = "Authorization",
                    description = "Bearer token for authentication",
                    required = true,
                    example = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            )
            @RequestHeader(value = "Authorization") String authorization,
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    name = "serviceIdentifier",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceIdentifier") String serviceIdentifier)
            throws ClientException, ServerException, DataProcessingException {
        UtilityBean<String> utilityBean =  getServiceAnomalyConfigBL.clientValidation(null,authorization, accountIdentifier, serviceIdentifier);
        utilityBean = getServiceAnomalyConfigBL.serverValidation(utilityBean);
        Map<String, ServiceSuppPersistenceConfigPojo> data = getServiceAnomalyConfigBL.process(utilityBean);
        ResponsePojo<Map<String, ServiceSuppPersistenceConfigPojo>> responseBean = new ResponsePojo<>("Service Configurations fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
